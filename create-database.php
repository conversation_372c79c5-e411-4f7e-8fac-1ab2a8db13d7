<?php
// Create database script
try {
    // Try different connection methods
    $connections = [
        ['mysql:host=127.0.0.1;port=3306', 'root', ''],
        ['mysql:host=localhost;port=3306', 'root', ''],
        ['mysql:host=127.0.0.1;port=3306', 'root', 'root'],
        ['mysql:host=localhost;port=3306', 'root', 'root'],
        ['mysql:host=127.0.0.1;port=3306', '', ''],
    ];

    $pdo = null;
    foreach ($connections as $conn) {
        try {
            echo "Trying connection: {$conn[0]} with user '{$conn[1]}'\n";
            $pdo = new PDO($conn[0], $conn[1], $conn[2]);
            echo "Connection successful!\n";
            break;
        } catch (PDOException $e) {
            echo "Failed: " . $e->getMessage() . "\n";
        }
    }

    if (!$pdo) {
        throw new Exception("Could not connect to MySQL with any method");
    }
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `naru` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database 'naru' created successfully!\n";
    
    // Select the database
    $pdo->exec("USE `naru`");
    
    // Import the SQL file
    $sqlFile = __DIR__ . '/database.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // Remove the database name from CREATE DATABASE statements if any
        $sql = preg_replace('/CREATE DATABASE.*?;/i', '', $sql);
        $sql = preg_replace('/USE.*?;/i', '', $sql);
        
        // Split into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                } catch (PDOException $e) {
                    // Skip errors for statements that might already exist
                    if (strpos($e->getMessage(), 'already exists') === false) {
                        echo "Warning: " . $e->getMessage() . "\n";
                    }
                }
            }
        }
        
        echo "Database imported successfully!\n";
    } else {
        echo "SQL file not found: $sqlFile\n";
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
