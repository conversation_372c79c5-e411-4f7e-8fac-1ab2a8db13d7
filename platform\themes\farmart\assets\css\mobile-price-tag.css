/* Mobile Price Tag Styles */
@media (max-width: 991px) {
    .mobile-price-tag {
        background-color: #ff6633 !important;
        margin: -10px -15px 15px -15px !important;
        padding: 10px 15px !important;
        width: calc(100% + 30px) !important;
        position: relative !important;
        color: #fff !important;
        display: block !important;
    }

    .mobile-price-tag .price-tag-content {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        height: 40px !important; /* Increased to match the height in product-item-grid and product-item-override */
    }

    .mobile-price-tag .sale-price {
        font-size: 18px !important;
        font-weight: 700 !important;
        margin-right: 10px !important;
        color: #fff !important;
    }

    .mobile-price-tag .regular-price {
        font-size: 14px !important;
        text-decoration: line-through !important;
        opacity: 0.8 !important;
        color: #fff !important;
    }

    .mobile-price-tag .regular-price-only {
        font-size: 18px !important;
        font-weight: 700 !important;
        color: #fff !important;
    }

    /* Fix for related products and more to love sections */
    .related-products-grid .product-inner .product-details > div,
    .more-to-love-products-grid .product-inner .product-details > div {
        min-height: 40px !important;
        height: 40px !important;
    }

    /* Adjust product details spacing */
    .product-details {
        padding-top: 0 !important;
    }

    .product-content-box {
        padding-top: 10px !important;
    }

    /* Hide desktop price on mobile */
    .product-details .d-none.d-md-block {
        display: none !important;
    }

    /* Force display for mobile price tag */
    .d-md-none.mobile-price-tag {
        display: block !important;
    }
}
